package com.socialplay.gpark.data.interactor

import android.content.Context
import androidx.annotation.LongDef
import com.meta.box.biz.h5config.ConfigurableH5Biz
import com.meta.box.biz.h5config.model.H5PageConfigItem
import com.socialplay.gpark.BuildConfig
import com.socialplay.gpark.R
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.ACTIVITY_ENTRANCE
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.APP_DOWNLOAD
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.BALANCE
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.DAILY_SIGN_WEB_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.LOGIN_HELP_DOC
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.MOD_PUBLISH_STATEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.MOD_PURCHASE_STATEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.PRIVACY_AGREEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.RECHARGE
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SPARK_ACCOUNT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SPARK_INSTRUCTION
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SEND_FLOWER_FEATURE_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SEND_FLOWER_FLOWER_RANK_README_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SEND_FLOWER_NOTICE_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.SEND_FLOWER_README_URL
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.USER_AGREEMENT
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.VIP_PLUS
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.VIP_STATUS
import com.socialplay.gpark.data.interactor.H5PageConfigInteractor.Companion.TRANSACTION_DETAILS
import com.socialplay.gpark.data.kv.MetaKV
import org.koin.core.context.GlobalContext
import retrofit2.Retrofit

@LongDef(
    RECHARGE,
    BALANCE,
    USER_AGREEMENT,
    PRIVACY_AGREEMENT,
    VIP_PLUS,
    VIP_STATUS,
    APP_DOWNLOAD,
    ACTIVITY_ENTRANCE,
    DAILY_SIGN_WEB_URL,
    SPARK_ACCOUNT,
    SPARK_INSTRUCTION,
    SEND_FLOWER_FEATURE_URL,
    SEND_FLOWER_NOTICE_URL,
    SEND_FLOWER_FLOWER_RANK_README_URL,
    SEND_FLOWER_README_URL,
    TRANSACTION_DETAILS,
    MOD_PUBLISH_STATEMENT,
    MOD_PURCHASE_STATEMENT,
    LOGIN_HELP_DOC
)
@Retention(AnnotationRetention.SOURCE)
annotation class H5PageCode


class H5PageConfigInteractor(context: Context, kv: MetaKV) {

    // 配置详情见文档：https://meta.feishu.cn/wiki/AtJbwVv6eih42QkMg9bcHx13nHc
    companion object {
        const val RECHARGE = 50L
        const val BALANCE = 51L
        const val USER_AGREEMENT = 3L
        const val PRIVACY_AGREEMENT = 4L
        const val VIP_PLUS = 54L
        const val VIP_STATUS = 52L
        const val APP_DOWNLOAD = 369L
        const val ACTIVITY_ENTRANCE = 368L
        const val DAILY_SIGN_WEB_URL = 370L
        const val SPARK_ACCOUNT = 400L
        const val SPARK_INSTRUCTION = 401L

        /**
         * 赠花功能协议-赠花功能弹窗使用
         */
        const val SEND_FLOWER_FEATURE_URL = 1200L

        /**
         * 送花须知
         */
        const val SEND_FLOWER_NOTICE_URL = 1201L

        /**
         * 鲜花榜单功能说明
         */
        const val SEND_FLOWER_FLOWER_RANK_README_URL = 1202L

        /**
         * 送花弹窗-标题的问号点击后跳转的页面
         */
        const val SEND_FLOWER_README_URL = 1203L

        /**
         * 交易明细
         */
        const val TRANSACTION_DETAILS = 420L

        /**
         * 资源库模组上架说明
         */
        const val MOD_PUBLISH_STATEMENT = 1204L

        /**
         * 模组购买说明
         */
        const val MOD_PURCHASE_STATEMENT = 1205L

        /**
         * 登录帮助文档
         */
        const val LOGIN_HELP_DOC = 1206L
    }

    private val defH5ConfigPages by lazy {
        setOf(
            H5PageConfigItem(RECHARGE, "", BuildConfig.RECHARGE_URL),
            H5PageConfigItem(BALANCE, "", BuildConfig.RECHARGE_DIALOG_URL),
            H5PageConfigItem(USER_AGREEMENT, context.getString(R.string.terms_of_service), BuildConfig.USER_AGREEMENT),
            H5PageConfigItem(PRIVACY_AGREEMENT, context.getString(R.string.privacy_policy), BuildConfig.PRIVACY_AGREEMENT),
            H5PageConfigItem(VIP_PLUS, "", BuildConfig.GPARK_PLUS),
            H5PageConfigItem(VIP_STATUS, "", BuildConfig.GPARK_PLUS_STATUS),
            H5PageConfigItem(APP_DOWNLOAD, "", BuildConfig.APP_DOWNLOAD),
            H5PageConfigItem(ACTIVITY_ENTRANCE, "", BuildConfig.GPARK_ACTIVITY_ENTRANCE),
            H5PageConfigItem(DAILY_SIGN_WEB_URL, "", BuildConfig.DAILY_SIGN_WEB_URL),
            H5PageConfigItem(SPARK_ACCOUNT, "", BuildConfig.SPARK_ACCOUNT),
            H5PageConfigItem(SPARK_INSTRUCTION, "", BuildConfig.SPARK_INSTRUCTION),
            H5PageConfigItem(SEND_FLOWER_FEATURE_URL, context.getString(R.string.send_flower_feature_h5_title), BuildConfig.SEND_FLOWER_FEATURE_URL),
            H5PageConfigItem(SEND_FLOWER_NOTICE_URL, context.getString(R.string.send_flower_notice_h5_title), BuildConfig.SEND_FLOWER_NOTICE_URL),
            H5PageConfigItem(SEND_FLOWER_FLOWER_RANK_README_URL, context.getString(R.string.send_flower_rank_readme_h5_title), BuildConfig.SEND_FLOWER_FLOWER_RANK_README_URL),
            H5PageConfigItem(SEND_FLOWER_README_URL, context.getString(R.string.send_flower_readme_h5_title), BuildConfig.SEND_FLOWER_README_URL),
            H5PageConfigItem(TRANSACTION_DETAILS, context.getString(R.string.transaction_details_page_title), BuildConfig.TRANSACTION_DETAILS_URL),
            H5PageConfigItem(MOD_PUBLISH_STATEMENT, context.getString(R.string.asset_comm_promo_content_4_guide_1), BuildConfig.MOD_PUBLISH_STATEMENT_URL),
            H5PageConfigItem(MOD_PURCHASE_STATEMENT, context.getString(R.string.asset_comm_promo_content_4_guide_2), BuildConfig.MOD_PURCHASE_STATEMENT_URL),
            H5PageConfigItem(LOGIN_HELP_DOC, context.getString(R.string.login_help_files), BuildConfig.MOD_PURCHASE_STATEMENT_URL),
        )
    }

    suspend fun init(isMainProcess: Boolean) {
        val retrofit = GlobalContext.get().get<Retrofit>()
        ConfigurableH5Biz.init(isMainProcess, retrofit, defH5ConfigPages, true)
    }

    fun getH5PageConfigItem(@H5PageCode code: Long): H5PageConfigItem {
        return ConfigurableH5Biz.getH5PageConfigItem(code)
    }

    fun getH5PageUrl(@H5PageCode code: Long): String {
        return getH5PageConfigItem(code).url
    }
}